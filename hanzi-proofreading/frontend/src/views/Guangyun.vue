<template>
  <div class="guangyun-page">
    <!-- 主内容区 -->
    <main class="main-content">
      <div class="content-wrapper">
        <section class="guangyun-section">

          <!-- 内容区 -->
          <div class="content-area">
            <!-- 校对结果区域 -->
            <div class="verified-result" v-if="currentCharacter">
              <div class="section-header">
                <div class="section-title">
                  <h3>广韵校对</h3>
                </div>
                <el-button
                  v-if="hasUnsavedChanges"
                  @click="saveAllChanges"
                  :loading="savingAll"
                  class="custom-save-all-button"
                  style="
                    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%) !important;
                    border: none !important;
                    border-radius: 8px !important;
                    padding: 12px 20px !important;
                    font-size: 14px !important;
                    font-weight: 600 !important;
                    color: white !important;
                    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3) !important;
                    display: flex !important;
                    align-items: center !important;
                    gap: 8px !important;
                  "
                >
                  <el-icon><Check /></el-icon>
                  全部保存
                </el-button>
              </div>

              <!-- 校对数据显示 -->
              <div class="pronunciations-container">
                <div
                  v-for="(pronunciation, index) in proofreadingData"
                  :key="index"
                  class="pronunciation-item"
                >
                  <div class="result-container">
                    <!-- 汉字显示区域 -->
                    <CharacterDisplay
                      :character="pronunciation"
                      :pronunciation-index="index"
                      :status-class="getStatusClass(pronunciation)"
                      :conflict-count="getConflictCount(pronunciation)"
                    />

                    <!-- 音韵信息区域 -->
                    <PhoneticInfo
                      :pronunciation="pronunciation"
                      :pronunciation-index="index"
                      @edit="handleEdit"
                    />

                    <!-- 保存按钮 -->
                    <div v-if="needsSave(pronunciation)" class="save-button" @click="savePronunciation(index)">
                      <el-icon><Check /></el-icon>
                    </div>
                  </div>

                  <!-- 冲突信息 - 独立显示区域 -->
                  <div v-if="getConflictInfo(pronunciation).length > 0" class="conflict-section">
                    <ConflictInfo
                      :conflicts="getConflictInfo(pronunciation)"
                      :unicode="pronunciation.unicode"
                      :fan-qie="pronunciation.fan_qie"
                      @conflict-updated="handleConflictUpdated"
                    />
                  </div>

                  <!-- 匹配的原始数据 -->
                  <div v-if="getMatchingSourceData(pronunciation.fan_qie).length > 0" class="matching-source-data">
                    <div class="source-grid">
                      <SourceDataCard
                        v-for="(source, sourceIndex) in getMatchingSourceData(pronunciation.fan_qie)"
                        :key="`matching-${source.source}-${sourceIndex}`"
                        :source="source"
                        :needs-ref-update="needsRefUpdate(source, pronunciation)"
                        :get-field-class="(fieldName, fieldValue, hanzi, currentSource) => getFieldClass(fieldName, fieldValue, hanzi, currentSource)"
                        @edit="handleEdit"
                        @save="saveSourceData"
                        @save-ref="(sourceRecord) => saveRefRelation(sourceRecord, pronunciation)"
                        @show-tooltip="showTooltip"
                        @hide-tooltip="hideTooltip"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 来源数据对比 -->
            <div class="source-data" v-if="getUnmatchedSourceData().length > 0">
              <h3>未匹配的原始数据</h3>
              <p class="section-desc">显示没有对应校对数据的原始数据。</p>

              <div class="source-grid">
                <SourceDataCard
                  v-for="(source, index) in getUnmatchedSourceData()"
                  :key="`unmatched-${source.source}-${index}`"
                  :source="source"
                  :show-add-button="true"
                  :get-field-class="(fieldName, fieldValue, hanzi, currentSource) => getFieldClass(fieldName, fieldValue, hanzi, currentSource)"
                  @edit="handleEdit"
                  @save="saveSourceData"
                  @add-to-proofreading="addSingleToPronunciationItems"
                  @show-tooltip="showTooltip"
                  @hide-tooltip="hideTooltip"
                />

                <!-- 添加字卡片 -->
                <div class="add-hanzi-card" @click="handleAddHanzi">
                  <div class="add-content">
                    <el-icon size="32"><Plus /></el-icon>
                    <span>添加汉字</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 校对记录 -->
            <div class="collation-log" v-if="currentCharacter">
              <h3>校对记录</h3>

              <!-- 加载状态 -->
              <div v-if="loadingLogs" class="loading-logs">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载校对记录中...</span>
              </div>

              <!-- 历史记录 -->
              <div v-else-if="proofreadingLogs.length > 0" class="log-history">
                <div class="log-list">
                  <LogEntryCard
                    v-for="log in proofreadingLogs"
                    :key="log.id"
                    :log="log"
                  />
                </div>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-logs">
                <el-empty description="暂无校对记录" :image-size="60" />
              </div>

              <!-- 加载更多 -->
              <div class="log-actions" v-if="hasMoreLogs && proofreadingLogs.length > 0">
                <el-button @click="loadMoreLogs" :loading="loadingLogs" size="small">
                  加载更多记录
                </el-button>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!currentCharacter && !loading" class="empty-state">
              <el-empty description="请输入汉字进行搜索">
                <el-button type="primary" @click="handleExampleSearch">查看示例</el-button>
              </el-empty>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-state" v-loading="loading" element-loading-text="正在查询广韵数据...">
              <div style="height: 200px;"></div>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- JavaScript控制的悬浮提示 -->
    <div
      v-if="tooltip.visible"
      class="js-tooltip"
      :style="{
        left: tooltip.x + 'px',
        top: tooltip.y + 'px'
      }"
    >
      {{ tooltip.content }}
    </div>

    <!-- 编辑对话框 -->
    <EditDialog
      v-model="editDialogVisible"
      :editing-field="editingField"
      :editing-value="editingValue"
      :field-options="getFieldOptions()"
      @update:editing-value="editingValue = $event"
      @confirm="confirmEdit"
      @cancel="cancelEdit"
    />
  </div>
</template>

<script>
import { Check, CircleCheck, Plus, Loading } from '@element-plus/icons-vue'
import CharacterDisplay from '@/components/guangyun/CharacterDisplay.vue'
import PhoneticInfo from '@/components/guangyun/PhoneticInfo.vue'
import SourceDataCard from '@/components/guangyun/SourceDataCard.vue'
import ConflictInfo from '@/components/ConflictInfo.vue'
import EditDialog from '@/components/guangyun/EditDialog.vue'
import LogEntryCard from '@/components/guangyun/LogEntryCard.vue'
import { GuangyunService } from '@/services/guangyunService'
import { GuangyunLogService } from '@/services/logService'
import { conflictApi } from '@/api/conflict'
import {
  getSourceName,
  getFieldClass,
  formatCurrentTime,
  parseHanziInput,
  normalizeFanQie
} from '@/utils/guangyunUtils'

export default {
  name: 'Guangyun',
  components: {
    CharacterDisplay,
    PhoneticInfo,
    SourceDataCard,
    ConflictInfo,
    EditDialog,
    LogEntryCard,
    Check,
    CircleCheck,
    Plus,
    Loading
  },
  data() {
    return {
      searchText: '大',
      loading: false,
      currentCharacter: null,
      proofreadingPronunciations: [],
      sourceData: [],
      conflictData: {}, // 存储冲突信息，key为unicode-fanqie
      tooltip: {
        visible: false,
        content: '',
        x: 0,
        y: 0
      },
      editingField: null,
      editingValue: '',
      editDialogVisible: false,
      savingAll: false,
      // 日志相关数据
      proofreadingLogs: [],
      loadingLogs: false,
      hasMoreLogs: true
    }
  },
  computed: {
    proofreadingData() {
      return this.proofreadingPronunciations || []
    },
    hasUnsavedChanges() {
      const hasProofreadingChanges = this.proofreadingPronunciations.some(pronunciation => pronunciation.hasChanges)
      const hasSourceChanges = this.sourceData.some(source => source.hasChanges)

      // 检查冲突数量是否有变化
      const hasConflictChanges = this.proofreadingPronunciations.some(pronunciation => {
        if (!pronunciation.id) return false // 新记录不检查冲突变化

        const currentConflictCount = this.getConflictCount(pronunciation)
        const originalConflictCount = pronunciation.originalConflictCount || 0

        return currentConflictCount !== originalConflictCount
      })

      // 检查是否有关联关系需要保存
      const hasRefChanges = this.proofreadingPronunciations.some(pronunciation => {
        if (!pronunciation.id) return false // 只检查已保存的校对数据

        const matchingSourceData = this.getMatchingSourceData(pronunciation.fan_qie)
        return matchingSourceData.some(source => this.needsRefUpdate(source, pronunciation))
      })

      return hasProofreadingChanges || hasSourceChanges || hasConflictChanges || hasRefChanges
    }
  },
  mounted() {
    this.loadExampleData()
  },
  methods: {
    // 工具方法
    getSourceName,
    formatCurrentTime,

    // 获取字段CSS类
    getFieldClass(fieldName, fieldValue, hanzi, currentSource) {
      return getFieldClass(fieldName, fieldValue, hanzi, currentSource, this.sourceData)
    },

    async loadExampleData() {
      await this.fetchGuangyunData('大')
    },

    // 供外部调用的搜索方法
    performSearch(query) {
      this.searchText = query
      this.handleSearch()
    },

    async handleSearch() {
      if (!this.searchText.trim()) {
        this.$message.warning('请输入要搜索的汉字或Unicode编码')
        this.$emit('search-loading', false)
        return
      }

      this.loading = true
      this.$emit('search-loading', true)

      try {
        // 解析输入内容（支持汉字字符和Unicode编码）
        const parsedInput = this.parseHanziInput(this.searchText.trim())

        if (parsedInput) {
          // 如果解析成功，使用解析后的字符进行搜索
          await this.fetchGuangyunData(parsedInput.character)
        } else {
          // 如果解析失败，尝试直接搜索（兼容原有逻辑）
          await this.fetchGuangyunData(this.searchText.trim())
        }
      } catch (error) {
        console.error('搜索失败:', error)
        this.$message.error('搜索失败，请检查输入的汉字或Unicode编码格式')
      } finally {
        this.loading = false
        this.$emit('search-loading', false)
      }
    },

    // 检查是否为中文汉字（支持所有Unicode汉字区域）
    isChineseCharacter(char) {
      if (!char || char.length === 0) return false

      const codePoint = char.codePointAt(0)

      // 基本汉字区域 (4E00-9FFF)
      if (codePoint >= 0x4E00 && codePoint <= 0x9FFF) return true

      // 扩展A区 (3400-4DBF)
      if (codePoint >= 0x3400 && codePoint <= 0x4DBF) return true

      // 扩展B区 (20000-2A6DF)
      if (codePoint >= 0x20000 && codePoint <= 0x2A6DF) return true

      // 扩展C区 (2A700-2B73F)
      if (codePoint >= 0x2A700 && codePoint <= 0x2B73F) return true

      // 扩展D区 (2B740-2B81F)
      if (codePoint >= 0x2B740 && codePoint <= 0x2B81F) return true

      // 扩展E区 (2B820-2CEAF)
      if (codePoint >= 0x2B820 && codePoint <= 0x2CEAF) return true

      // 扩展F区 (2CEB0-2EBEF)
      if (codePoint >= 0x2CEB0 && codePoint <= 0x2EBEF) return true

      // 扩展G区 (30000-3134F)
      if (codePoint >= 0x30000 && codePoint <= 0x3134F) return true

      // 兼容汉字 (F900-FAFF)
      if (codePoint >= 0xF900 && codePoint <= 0xFAFF) return true

      // 兼容汉字补充 (2F800-2FA1F)
      if (codePoint >= 0x2F800 && codePoint <= 0x2FA1F) return true

      return false
    },

    // 解析用户输入的汉字或Unicode编码
    parseHanziInput(input) {
      if (!input) return null

      // 情况1: 直接输入汉字
      if (input.length >= 1 && this.isChineseCharacter(input)) {
        const codePoint = input.codePointAt(0)
        const unicodeCode = codePoint.toString(16).toUpperCase()
        return {
          character: input,
          unicode_code: unicodeCode,
          unicode: `U+${unicodeCode}`,
          metadata_entries: []
        }
      }

      // 情况2: 输入Unicode编码
      let unicodeCode = ''

      // 处理各种Unicode编码格式
      if (input.startsWith('U+') || input.startsWith('u+')) {
        unicodeCode = input.substring(2).toUpperCase()
      } else if (input.startsWith('0x') || input.startsWith('0X')) {
        unicodeCode = input.substring(2).toUpperCase()
      } else if (/^[0-9A-Fa-f]+$/.test(input)) {
        unicodeCode = input.toUpperCase()
      } else {
        return null
      }

      // 验证Unicode编码格式
      if (!/^[0-9A-F]+$/.test(unicodeCode)) {
        return null
      }

      // 转换为字符
      try {
        const codePoint = parseInt(unicodeCode, 16)
        const character = String.fromCodePoint(codePoint)

        // 验证是否为有效的汉字
        if (!this.isChineseCharacter(character)) {
          return null
        }

        return {
          character: character,
          unicode_code: unicodeCode,
          unicode: `U+${unicodeCode}`,
          metadata_entries: []
        }
      } catch (error) {
        return null
      }
    },

    // 获取广韵数据
    async fetchGuangyunData(character) {
      try {
        const data = await GuangyunService.fetchGuangyunData(character)

        // 处理校对数据
        this.proofreadingPronunciations = GuangyunService.processProofreadingData(data.merged_data)

        // 设置当前字符
        if (this.proofreadingPronunciations.length > 0) {
          this.currentCharacter = this.proofreadingPronunciations[0]

          // 加载徽章
          const badges = await GuangyunService.loadHanziBadges(character, this.currentCharacter.unicode)
          this.proofreadingPronunciations.forEach(pronunciation => {
            pronunciation.badges = badges
          })

          // 加载冲突信息
          await this.loadConflictData()
        } else {
          // 创建空的当前字符
          const unicodeCode = character.codePointAt(0).toString(16).toUpperCase().padStart(4, '0')
          this.currentCharacter = {
            hanzi: character,
            unicode: `U+${unicodeCode}`,
            badges: []
          }
        }

        // 处理来源数据
        this.sourceData = GuangyunService.processSourceData(data.sources)

        // 显示提示信息
        this.showDataStatusMessage(data, character)

        // 加载校对记录日志
        await this.loadProofreadingLogs()

      } catch (error) {
        console.error('获取广韵数据失败:', error)
        this.$message.error(`获取广韵数据失败: ${error.message}`)
        this.currentCharacter = null
        this.sourceData = []
        this.proofreadingLogs = []
      }
    },

    // 显示数据状态消息
    showDataStatusMessage(data, character) {
      if (!data.has_proofreading_data && data.source_count === 0) {
        this.$message.info(`汉字 "${character}" 暂无校对数据和原始数据`)
      } else if (!data.has_proofreading_data && data.source_count > 0) {
        this.$message.info(`汉字 "${character}" 暂无校对数据，但有原始数据`)
      } else if (data.has_proofreading_data && data.source_count === 0) {
        this.$message.info(`汉字 "${character}" 有校对数据，但暂无原始数据`)
      }
    },

    // 获取匹配的原始数据
    getMatchingSourceData(fanQie) {
      return GuangyunService.getMatchingSourceData(fanQie, this.sourceData)
    },

    // 获取未匹配的原始数据
    getUnmatchedSourceData() {
      return GuangyunService.getUnmatchedSourceData(this.proofreadingPronunciations, this.sourceData)
    },

    // 获取状态类
    getStatusClass(pronunciation) {
      if (!pronunciation) {
        return 'success'
      }

      const conflictCount = this.getConflictCount(pronunciation)

      if (conflictCount === 0) {
        return 'success'
      } else if (conflictCount <= 2) {
        return 'warning'
      } else {
        return 'error'
      }
    },

    // 获取冲突数量
    getConflictCount(pronunciation) {
      if (!pronunciation || !pronunciation.fan_qie || pronunciation.fan_qie === '-') {
        return 0
      }

      // 获取匹配的原始数据
      const matchingData = this.getMatchingSourceData(pronunciation.fan_qie)

      if (matchingData.length < 2) {
        return 0 // 少于2个来源，无法比较
      }

      // 需要比较的字段
      const fieldsToCompare = [
        'fan_qie', 'sheng_mu', 'yun_bu', 'sheng_diao',
        'kai_he', 'deng_di', 'xiao_yun', 'qing_zhuo', 'she'
      ]

      let conflictCount = 0

      // 检查每个字段是否有冲突
      fieldsToCompare.forEach(fieldName => {
        const fieldValues = new Set()

        matchingData.forEach(source => {
          let value = source[fieldName]

          // 对反切字段进行标准化处理
          if (fieldName === 'fan_qie' && value && value !== '-' && value.trim() !== '') {
            value = normalizeFanQie(value, source.source)
          }

          // 只统计有效值
          if (value && value !== '-' && value.trim() !== '') {
            fieldValues.add(value.trim())
          }
        })

        // 如果有多个不同的值，则该字段有冲突
        if (fieldValues.size > 1) {
          conflictCount++
        }
      })

      return conflictCount
    },

    // 获取来源名称列表
    getSourceNames() {
      return this.sourceData.map(s => getSourceName(s.source)).join('、')
    },

    // 检查是否需要保存
    needsSave(pronunciation) {
      // 检查字段是否有变化
      if (pronunciation.hasChanges) {
        return true
      }

      // 检查冲突数量是否有变化
      if (pronunciation.id) {
        const currentConflictCount = this.getConflictCount(pronunciation)
        const originalConflictCount = pronunciation.originalConflictCount || 0
        return currentConflictCount !== originalConflictCount
      }

      return false
    },

    // 处理编辑
    handleEdit(editInfo) {
      this.editingField = editInfo
      this.editingValue = editInfo.currentValue === '-' ? '' : (editInfo.currentValue || '')
      this.editDialogVisible = true
    },

    // 确认编辑
    confirmEdit() {
      if (!this.editingField) return

      const newValue = this.editingValue.trim() || '-'

      if (this.editingField.isSourceData) {
        // 编辑原始数据
        const { sourceRecord, fieldName } = this.editingField
        sourceRecord[fieldName] = newValue
        sourceRecord.hasChanges = true
      } else {
        // 编辑校对数据
        const { pronunciationIndex, fieldName } = this.editingField
        const pronunciation = this.proofreadingPronunciations[pronunciationIndex]
        pronunciation[fieldName] = newValue
        pronunciation.hasChanges = true
      }

      this.editDialogVisible = false
      this.editingField = null
      this.editingValue = ''
    },

    // 取消编辑
    cancelEdit() {
      this.editDialogVisible = false
      this.editingField = null
      this.editingValue = ''
    },

    // 处理示例搜索
    handleExampleSearch() {
      this.searchText = '烟'
      this.handleSearch()
    },

    // 获取字段选项
    getFieldOptions() {
      if (!this.editingField || this.editingField.isSourceData) {
        return []
      }

      const { pronunciationIndex, fieldName } = this.editingField
      const pronunciation = this.proofreadingPronunciations[pronunciationIndex]

      if (!pronunciation) {
        return []
      }

      // 获取匹配的原始数据
      const matchingData = this.getMatchingSourceData(pronunciation.fan_qie)

      // 提取该字段的所有不同值
      const fieldValues = new Set()
      const options = []

      matchingData.forEach(source => {
        const value = source[fieldName]
        if (value && value !== '-' && value.trim() !== '') {
          const normalizedValue = value.trim()
          if (!fieldValues.has(normalizedValue)) {
            fieldValues.add(normalizedValue)
            options.push({
              value: normalizedValue,
              label: normalizedValue,
              source: getSourceName(source.source)
            })
          }
        }
      })

      // 按来源排序
      const sourceOrder = { '小学堂': 1, '全息': 2, '韵典': 3 }
      options.sort((a, b) => {
        const orderA = sourceOrder[a.source] || 999
        const orderB = sourceOrder[b.source] || 999
        if (orderA !== orderB) {
          return orderA - orderB
        }
        return a.label.localeCompare(b.label)
      })

      return options
    },

    // 保存校对数据
    async savePronunciation(pronunciationIndex) {
      const pronunciation = this.proofreadingPronunciations[pronunciationIndex]

      try {
        // 计算冲突数量
        const conflictCount = this.getConflictCount(pronunciation)

        await GuangyunService.savePronunciation(pronunciation, conflictCount)

        // 保存成功后更新原始冲突数量
        pronunciation.originalConflictCount = conflictCount

        this.$message.success('保存成功')
      } catch (error) {
        console.error('保存失败:', error)
        this.$message.error('保存失败：' + error.message)
      }
    },

    // 保存原始数据
    async saveSourceData(sourceRecord) {
      try {
        await GuangyunService.saveSourceData(sourceRecord)
        this.$message.success('保存成功')
      } catch (error) {
        console.error('保存原始数据失败:', error)
        this.$message.error('保存失败：' + error.message)
      }
    },

    // 检查是否需要更新ref关系
    needsRefUpdate(sourceRecord, pronunciationItem) {
      return !sourceRecord.ref || sourceRecord.ref !== pronunciationItem.id
    },

    // 保存关联关系
    async saveRefRelation(sourceRecord, pronunciationItem) {
      if (!sourceRecord.id || !pronunciationItem.id) {
        this.$message.error('无法保存关联：缺少记录ID')
        return
      }

      try {
        // 更新ref字段
        sourceRecord.ref = pronunciationItem.id
        await GuangyunService.saveSourceData(sourceRecord)
        this.$message.success('关联关系保存成功')
      } catch (error) {
        console.error('保存关联关系失败:', error)
        this.$message.error('保存关联失败：' + error.message)
      }
    },

    // 添加单个原始数据到校对数据
    addSingleToPronunciationItems(sourceData) {
      const newPronunciationItem = {
        id: null,
        hanzi: sourceData.hanzi,
        unicode: sourceData.unicode,
        fan_qie: sourceData.fan_qie || '-',
        sheng_mu: sourceData.sheng_mu || '-',
        yun_bu: sourceData.yun_bu || '-',
        sheng_diao: sourceData.sheng_diao || '-',
        kai_he: sourceData.kai_he || '-',
        deng_di: sourceData.deng_di || '-',
        xiao_yun: sourceData.xiao_yun || '-',
        qing_zhuo: sourceData.qing_zhuo || '-',
        she: sourceData.she || '-',
        shi_yi: sourceData.shi_yi || '-',
        badges: this.currentCharacter?.badges || [],
        isEditing: false,
        hasChanges: true,
        originalData: {},
        isNewItem: true
      }

      newPronunciationItem.originalData = { ...newPronunciationItem }
      this.proofreadingPronunciations.push(newPronunciationItem)
      this.$message.success(`已添加来自${getSourceName(sourceData.source)}的数据到校对列表`)
    },

    // 处理添加汉字
    handleAddHanzi() {
      this.$prompt('请输入汉字或Unicode编码', '添加汉字', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.+$/,
        inputErrorMessage: '请输入有效的汉字或Unicode编码',
        inputPlaceholder: '例如：汉 或 6C49 或 U+6C49'
      }).then(({ value }) => {
        const newHanzi = parseHanziInput(value.trim())
        if (newHanzi) {
          this.addNewHanziData(newHanzi.character)
        } else {
          this.$message.error('无法识别输入的内容，请检查格式')
        }
      }).catch(() => {
        // 用户取消了输入
      })
    },

    // 添加新汉字数据
    async addNewHanziData(character) {
      try {
        this.loading = true

        // 检查是否已经存在该汉字的数据
        const existingData = this.sourceData.find(source => source.hanzi === character)
        if (existingData) {
          this.$message.warning(`汉字"${character}"的数据已存在`)
          return
        }

        // 获取新汉字的数据并添加到现有列表
        const data = await GuangyunService.fetchGuangyunData(character)

        if (!data.sources || Object.keys(data.sources).length === 0) {
          this.$message.warning(`未找到汉字"${character}"的广韵数据`)
          return
        }

        // 将新数据添加到sourceData数组中
        const newSourceData = GuangyunService.processSourceData(data.sources)
        this.sourceData.push(...newSourceData)

        this.$message.success(`成功添加汉字"${character}"的数据`)

      } catch (error) {
        console.error('添加汉字数据失败:', error)
        this.$message.error('添加汉字数据失败，请重试')
      } finally {
        this.loading = false
      }
    },

    // 显示提示
    showTooltip(event, content) {
      if (!content || content === '暂无释义') return

      const rect = event.target.getBoundingClientRect()
      const scrollX = window.pageXOffset || document.documentElement.scrollLeft
      const scrollY = window.pageYOffset || document.documentElement.scrollTop

      this.tooltip = {
        visible: true,
        content: content,
        x: rect.left + scrollX,
        y: rect.bottom + scrollY + 4
      }
    },

    // 隐藏提示
    hideTooltip() {
      this.tooltip.visible = false
    },

    // 查找最匹配的校对数据
    findBestMatchingPronunciation(source) {
      const candidates = this.proofreadingPronunciations.filter(pronunciation => {
        return pronunciation.id &&
               pronunciation.hanzi === source.hanzi &&
               pronunciation.unicode === source.unicode
      })

      if (candidates.length === 0) return null
      if (candidates.length === 1) return candidates[0]

      // 多个候选时，使用音韵特征进行精确匹配
      let bestMatch = null
      let maxScore = -1

      for (const candidate of candidates) {
        let score = 0

        // 反切匹配得分最高
        if (source.fan_qie && candidate.fan_qie &&
            source.fan_qie !== '-' && candidate.fan_qie !== '-') {
          if (normalizeFanQie(source.fan_qie) === normalizeFanQie(candidate.fan_qie)) {
            score += 10
          }
        }

        // 其他字段匹配
        const matchingFields = ['sheng_mu', 'yun_bu', 'sheng_diao', 'kai_he', 'deng_di']
        for (const field of matchingFields) {
          if (source[field] && candidate[field] &&
              source[field] !== '-' && candidate[field] !== '-' &&
              source[field] === candidate[field]) {
            score += 1
          }
        }

        if (score > maxScore) {
          maxScore = score
          bestMatch = candidate
        }
      }

      return bestMatch || candidates[0]
    },

    // 全部保存功能
    async saveAllChanges() {
      if (!this.hasUnsavedChanges) {
        this.$message.info('没有需要保存的更改')
        return
      }

      this.savingAll = true
      let savedCount = 0
      let errorCount = 0

      try {
        // 保存校对数据
        for (const pronunciation of this.proofreadingPronunciations) {
          // 检查是否有字段变化或冲突数量变化
          const currentConflictCount = this.getConflictCount(pronunciation)
          const originalConflictCount = pronunciation.originalConflictCount || 0
          const hasConflictChange = pronunciation.id && (currentConflictCount !== originalConflictCount)

          if (pronunciation.hasChanges || hasConflictChange) {
            try {
              await GuangyunService.savePronunciation(pronunciation, currentConflictCount)

              // 保存成功后更新原始冲突数量
              pronunciation.originalConflictCount = currentConflictCount

              savedCount++
            } catch (error) {
              console.error('保存校对数据失败:', error)
              errorCount++
            }
          }
        }

        // 保存原始数据
        for (const source of this.sourceData) {
          if (source.hasChanges) {
            try {
              await GuangyunService.saveSourceData(source)
              savedCount++
            } catch (error) {
              console.error('保存原始数据失败:', error)
              errorCount++
            }
          }
        }

        // 保存关联关系
        for (const pronunciation of this.proofreadingPronunciations) {
          if (pronunciation.id) { // 只处理已保存的校对数据
            const matchingSourceData = this.getMatchingSourceData(pronunciation.fan_qie)

            for (const source of matchingSourceData) {
              if (this.needsRefUpdate(source, pronunciation)) {
                try {
                  await this.saveRefRelation(source, pronunciation)
                  savedCount++
                } catch (error) {
                  console.error('保存关联关系失败:', error)
                  errorCount++
                }
              }
            }
          }
        }

        // 显示保存结果
        if (errorCount === 0 && savedCount > 0) {
          this.$message.success(`全部保存成功！保存 ${savedCount} 条记录`)
        } else if (savedCount > 0) {
          this.$message.warning(`部分保存成功：保存 ${savedCount} 条记录，${errorCount} 项失败`)
        } else if (errorCount > 0) {
          this.$message.error(`保存失败：${errorCount} 项操作失败`)
        } else {
          this.$message.info('没有需要保存的更改')
        }

        // 重新获取数据以更新界面（包括日志）
        if (savedCount > 0) {
          await this.fetchGuangyunData(this.currentCharacter.hanzi)
        }

      } catch (error) {
        console.error('全部保存过程中发生错误:', error)
        this.$message.error('保存过程中发生错误，请稍后重试')
      } finally {
        this.savingAll = false
      }
    },

    // 日志相关方法
    // 加载校对记录日志
    async loadProofreadingLogs() {
      if (!this.currentCharacter) return

      this.loadingLogs = true
      try {
        const logs = await GuangyunLogService.getHanziLogs(this.currentCharacter.hanzi, 20)
        this.proofreadingLogs = logs || []
        this.hasMoreLogs = logs && logs.length >= 20
      } catch (error) {
        console.error('加载校对记录失败:', error)
        this.proofreadingLogs = []
      } finally {
        this.loadingLogs = false
      }
    },

    // 加载更多日志
    async loadMoreLogs() {
      if (!this.currentCharacter || this.loadingLogs) return

      this.loadingLogs = true
      try {
        const currentCount = this.proofreadingLogs.length
        const logs = await GuangyunLogService.getHanziLogs(this.currentCharacter.hanzi, currentCount + 20)

        if (logs && logs.length > currentCount) {
          this.proofreadingLogs = logs
          this.hasMoreLogs = logs.length >= currentCount + 20
        } else {
          this.hasMoreLogs = false
        }
      } catch (error) {
        console.error('加载更多日志失败:', error)
        this.hasMoreLogs = false
      } finally {
        this.loadingLogs = false
      }
    },

    // 格式化日志时间
    formatLogTime(timeStr) {
      return GuangyunLogService.formatLogTime(timeStr)
    },

    // 加载冲突数据
    async loadConflictData() {
      try {
        // 为每个发音记录加载冲突信息
        for (const pronunciation of this.proofreadingPronunciations) {
          const conflictKey = `${pronunciation.unicode}-${pronunciation.fan_qie}`

          try {
            // 移除U+前缀，只传送纯Unicode编码
            const cleanUnicode = pronunciation.unicode.replace('U+', '').replace('u+', '')

            const conflicts = await conflictApi.getConflictsList({
              unicode: cleanUnicode,
              fan_qie: pronunciation.fan_qie,
              limit: 20
            })

            this.conflictData[conflictKey] = conflicts || []
          } catch (error) {
            console.error(`加载冲突数据失败 (${conflictKey}):`, error)
            this.conflictData[conflictKey] = []
          }
        }
      } catch (error) {
        console.error('加载冲突数据失败:', error)
      }
    },

    // 获取冲突信息
    getConflictInfo(pronunciation) {
      const conflictKey = `${pronunciation.unicode}-${pronunciation.fan_qie}`
      return this.conflictData[conflictKey] || []
    },

    // 处理冲突更新
    handleConflictUpdated(updatedConflict) {
      // 更新本地冲突数据
      const conflictKey = `${updatedConflict.unicode}-${updatedConflict.fan_qie}`
      const conflicts = this.conflictData[conflictKey] || []

      const index = conflicts.findIndex(c => c.id === updatedConflict.id)
      if (index !== -1) {
        conflicts[index] = updatedConflict
      }

      // 触发响应式更新 (Vue 3 方式)
      this.conflictData[conflictKey] = [...conflicts]

      console.log('冲突状态已更新:', updatedConflict)
    }

  }
}
</script>

<style scoped>
.guangyun-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
}

.main-content {
  flex: 1;
}

.content-wrapper {
  max-width: 1536px;
  margin: 0 auto;
}

.guangyun-section {
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.content-area {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 640px) {
  .content-area {
    padding: 1rem;
  }
}

.verified-result h3,
.source-data h3,
.collation-log h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.section-desc {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0.25rem 0 1rem 0;
}

.verified-result {
  margin-bottom: 2rem;
}

.pronunciations-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.pronunciation-item {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.result-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

@media (min-width: 768px) {
  .result-container {
    flex-direction: row;
    align-items: flex-start;
  }
}

.result-container .save-button {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  transition: all 0.2s;
}

.result-container .save-button:hover {
  background: #059669;
  transform: scale(1.1);
}

/* 冲突信息独立区域样式 */
.conflict-section {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-top: 1rem;
  overflow: hidden;
}

.matching-source-data {
  margin-top: 1.5rem;
}

.source-data {
  margin-bottom: 2rem;
}

.source-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .source-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.add-hanzi-card {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 2px dashed #d1d5db;
  transition: all 0.2s;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.add-hanzi-card:hover {
  border-color: #6366f1;
  background-color: #f8fafc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.add-hanzi-card:hover .add-content {
  color: #6366f1;
}

.collation-log {
  margin-bottom: 2rem;
}

.log-card {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
}

.log-content {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.log-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.log-time {
  font-family: 'Monaco', 'Menlo', monospace;
  color: var(--text-secondary);
}

.log-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-icon.success {
  color: #16a34a;
}

.status-text {
  font-weight: 500;
  color: var(--text-primary);
}

.empty-state,
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 20rem;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.section-title {
  flex: 1;
}

.section-title h3 {
  margin: 0 0 0.5rem 0;
}

/* 自定义全部保存按钮样式 */
.custom-save-all-button {
  flex-shrink: 0;
  margin-left: 1rem;
  transition: all 0.3s ease !important;
}

.custom-save-all-button:hover {
  background: linear-gradient(135deg, #357ABD 0%, #2E6BA8 100%) !important;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4) !important;
  transform: translateY(-1px) !important;
}

.custom-save-all-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3) !important;
}

.custom-save-all-button.is-loading {
  opacity: 0.8 !important;
}

.custom-save-all-button .el-icon {
  font-size: 16px !important;
  margin-right: 4px !important;
}

.js-tooltip {
  position: absolute;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 8px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  max-width: 400px;
  white-space: normal;
  line-height: 1.4;
  font-size: 14px;
  color: #374151;
  pointer-events: none;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  will-change: transform;
  opacity: 1;
  visibility: visible;
}

/* 日志相关样式 */
.log-history {
  margin-top: 16px;
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.empty-logs,
.loading-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin-top: 16px;
}

.loading-logs {
  gap: 8px;
}

.log-actions {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

</style>